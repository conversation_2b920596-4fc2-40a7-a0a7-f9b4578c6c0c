[{"code": "6467", "procedure_code": "i0934", "test_profile": "liquid profile", "test_price": "01", "discount_price": "01", "emergency_price": "01", "home_visit_price": "01", "discount": "01", "category": "Standard", "test_count": "2", "is_active": true, "description": "testing", "testItems": [{"test_id": 175, "testName": "MCHC", "amount": 0}, {"test_id": 278, "testName": "Electrophoresis - HB", "amount": 0}], "currentTest": {"test_id": null, "testName": "", "amount": 0}, "gstRate": 18, "id": "f66ca840-5a91-4e9f-acf0-5b6d19a05f6d"}, {"code": "P00024", "procedure_code": "wew", "test_profile": "wewq", "test_price": "02", "discount_price": "02", "emergency_price": "02", "home_visit_price": "02", "discount": "02", "category": "Standard", "test_count": "02", "is_active": true, "description": "jkhk", "testItems": [{"test_id": 2, "testName": "Abs.Eosinophils in #.", "amount": 0}, {"test_id": 49, "testName": "NASAL SMEAR FOR EOSINOPHILS", "amount": 0}], "currentTest": {"test_id": null, "testName": "", "amount": 0}, "gstRate": 18, "id": "994c11af-3f22-412c-9c79-8b742109b8a9"}, {"code": "testing profile master", "procedure_code": "90829031", "test_profile": "testing profile", "test_price": "02", "discount_price": "02", "emergency_price": "02", "home_visit_price": "02", "discount": "02", "category": "Standard", "test_count": "02", "is_active": true, "description": "test", "testItems": [{"test_id": 48, "testName": "MYOGLOBIN-SERUM", "amount": 0}, {"test_id": 12, "testName": "Chromosome Analysis - Product of Conception", "amount": 0}], "currentTest": {"test_id": null, "testName": "", "amount": 0}, "gstRate": 18, "id": "1535110d-a306-4d07-ade6-2c12d781d085"}, {"code": "P000242", "procedure_code": "23", "test_profile": "new profile", "test_price": "02", "discount_price": "02", "emergency_price": "02", "home_visit_price": "02", "discount": "02", "category": "Standard", "test_count": "02", "is_active": true, "description": "test", "testItems": [{"test_id": 49, "testName": "NASAL SMEAR FOR EOSINOPHILS", "amount": 0}, {"test_id": 21, "testName": "ESR", "amount": 0}], "currentTest": {"test_id": null, "testName": "", "amount": 0}, "gstRate": 18, "id": "1ae4e947-5b16-4fac-9c13-c3ddc008785c"}, {"code": "p1", "procedure_code": "p1", "test_profile": "Liquid Profile", "test_price": "400", "discount_price": "", "emergency_price": "", "home_visit_price": "", "discount": "", "category": "Standard", "test_count": "7", "is_active": true, "description": "test", "testItems": [{"test_id": 445, "testName": "<PERSON><PERSON><PERSON><PERSON>, Total", "amount": 0}, {"test_id": 442, "testName": "Cholesterol, HDL", "amount": 0}, {"test_id": 443, "testName": "Cholesterol, LDL", "amount": 0}, {"test_id": 447, "testName": "Cholesterol/HDL Ratio", "amount": 0}, {"test_id": 446, "testName": "Cholesterol, VLDL", "amount": 0}, {"test_id": 526, "testName": "LDL/HDL Ratio", "amount": 0}, {"test_id": 583, "testName": "Triglycerides", "amount": 0}], "currentTest": {"test_id": null, "testName": "", "amount": 0}, "gstRate": 18, "id": "01464b61-469c-439f-aba7-a7a6814c6406"}]